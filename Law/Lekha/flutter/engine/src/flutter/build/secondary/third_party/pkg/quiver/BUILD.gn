# Copyright 2021 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# TODO(flutter/flutter#85356): This file was originally generated by the
# fuchsia.git script: `package_importer.py`. The generated `BUILD.gn` files were
# copied to the flutter repo to support `dart_library` targets used for
# Flutter-Fuchsia integration tests. This file can be maintained by hand, but it
# would be better to implement a script for Flutter, to either generate these
# BUILD.gn files or dynamically generate the GN targets.

import("//flutter/tools/fuchsia/dart/dart_library.gni")

dart_library("quiver") {
  package_name = "quiver"

  # The current version of this library is not null safe
  language_version = "2.0"

  deps = [
    "$dart_src/pkg/meta",
    "$dart_src/third_party/pkg/matcher",
  ]

  sources = [
    "async.dart",
    "cache.dart",
    "check.dart",
    "collection.dart",
    "core.dart",
    "io.dart",
    "iterables.dart",
    "mirrors.dart",
    "pattern.dart",
    "src/async/collect.dart",
    "src/async/concat.dart",
    "src/async/countdown_timer.dart",
    "src/async/enumerate.dart",
    "src/async/future_stream.dart",
    "src/async/iteration.dart",
    "src/async/metronome.dart",
    "src/async/stream_buffer.dart",
    "src/async/stream_router.dart",
    "src/async/string.dart",
    "src/cache/cache.dart",
    "src/cache/map_cache.dart",
    "src/collection/bimap.dart",
    "src/collection/delegates/iterable.dart",
    "src/collection/delegates/list.dart",
    "src/collection/delegates/map.dart",
    "src/collection/delegates/queue.dart",
    "src/collection/delegates/set.dart",
    "src/collection/lru_map.dart",
    "src/collection/multimap.dart",
    "src/collection/treeset.dart",
    "src/core/hash.dart",
    "src/core/optional.dart",
    "src/iterables/concat.dart",
    "src/iterables/count.dart",
    "src/iterables/cycle.dart",
    "src/iterables/enumerate.dart",
    "src/iterables/generating_iterable.dart",
    "src/iterables/infinite_iterable.dart",
    "src/iterables/merge.dart",
    "src/iterables/min_max.dart",
    "src/iterables/partition.dart",
    "src/iterables/range.dart",
    "src/iterables/zip.dart",
    "src/time/clock.dart",
    "src/time/duration_unit_constants.dart",
    "src/time/util.dart",
    "strings.dart",
    "testing/async.dart",
    "testing/equality.dart",
    "testing/runtime.dart",
    "testing/src/async/fake_async.dart",
    "testing/src/equality/equality.dart",
    "testing/src/runtime/checked_mode.dart",
    "testing/src/time/time.dart",
    "testing/time.dart",
    "time.dart",
  ]
}
