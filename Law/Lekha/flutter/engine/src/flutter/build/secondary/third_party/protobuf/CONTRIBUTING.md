# Contributing to Fuchsia

Fuchsia lets anyone contribute to the project, regardless of their employer.
The Fuchsia project reviews and encourages well-tested, high-quality
contributions from anyone who wants to contribute to Fuchsia.

## Contributor License Agreement

Contributions to this project must be accompanied by a Contributor License
Agreement (CLA).

To see any Contributor License Agreements on file or to sign a CLA, go to <https://cla.developers.google.com/>.

For more information about the Google CLA, see [Contributor License Agreements](https://cla.developers.google.com/about).

## Contributing changes and submitting code reviews

All changes require review, including changes by project members.

For detailed instructions on how to contribute changes,
see [Contribute changes](/docs/development/source_code/contribute_changes.md).

## Community guidelines

This project observes the following community guidelines:

  * [Google's Open Source Community Guidelines](https://opensource.google/conduct/)

  * [Fuchsia Code of Conduct](/docs/CODE_OF_CONDUCT.md)

## Governance

Review Fuchsia's [Governance](/docs/contribute/governance/governance.md)
statement.
