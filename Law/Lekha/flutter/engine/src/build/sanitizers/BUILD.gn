# Copyright (c) 2015 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

if (is_linux && !is_chromeos) {
  # TODO(GYP): Figure out which of these work and are needed on other platforms.
  copy("copy_llvm_symbolizer") {
    if (is_win) {
      sources =
          [ "//third_party/llvm-build/Release+Asserts/bin/llvm-symbolizer.exe" ]
      outputs = [ "$root_out_dir/llvm-symbolizer.exe" ]
    } else {
      sources =
          [ "//third_party/llvm-build/Release+Asserts/bin/llvm-symbolizer" ]
      outputs = [ "$root_out_dir/llvm-symbolizer" ]
    }
  }
}
