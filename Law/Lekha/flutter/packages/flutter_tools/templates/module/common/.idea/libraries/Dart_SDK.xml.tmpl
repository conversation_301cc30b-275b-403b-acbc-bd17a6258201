<component name="libraryTable">
  <library name="Dart SDK">
    <CLASSES>
      <root url="file://{{{dartSdk}}}/lib/async" />
      <root url="file://{{{dartSdk}}}/lib/collection" />
      <root url="file://{{{dartSdk}}}/lib/convert" />
      <root url="file://{{{dartSdk}}}/lib/core" />
      <root url="file://{{{dartSdk}}}/lib/developer" />
      <root url="file://{{{dartSdk}}}/lib/html" />
      <root url="file://{{{dartSdk}}}/lib/io" />
      <root url="file://{{{dartSdk}}}/lib/isolate" />
      <root url="file://{{{dartSdk}}}/lib/math" />
      <root url="file://{{{dartSdk}}}/lib/mirrors" />
      <root url="file://{{{dartSdk}}}/lib/typed_data" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>