<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		jqU87p/MJtpIAzfj0wm59ZtbI3o=
		</data>
		<key>Resources/PrivacyInfo.xcprivacy</key>
		<data>
		B3Chzxf8P+/dQrjYEe6D43q2Ggo=
		</data>
		<key>Resources/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			pTAmoSzOv/HLvofRvBZyxNfJ1Op07jMJo1yFlNnuj30=
			</data>
		</dict>
		<key>Headers/FlutterAppLifecycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			oq+QhyFXFL2i2XIKH6dl24wcr0T3n22ps2+oVnGQrOc=
			</data>
		</dict>
		<key>Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			h6MlxskInyxP7IIVoexxen/A/U19LF8qgv4WuJDDuuc=
			</data>
		</dict>
		<key>Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>Headers/FlutterMacOS.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JjZR2RGsnVAbyM4Ogsw4m5m8563NSHL09XAcsF9jXVo=
			</data>
		</dict>
		<key>Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SxKPDFth/SkJWcD6ua8FjRXytwtIhU38hBUY/kLuzAo=
			</data>
		</dict>
		<key>Headers/FlutterPluginMacOS.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JZlDr05qFxzTIzU18aVTKM3NeffuDiIg/vWvxM17qVo=
			</data>
		</dict>
		<key>Headers/FlutterPluginRegistrarMacOS.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DngirKkW2VbOA4sWFN27fp+1t8usdFgB7Oc0JHbAVyk=
			</data>
		</dict>
		<key>Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SMax8YOIA05ylkjLxEHZOhOa5jFK+Onca58D7h4qpjo=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			wgsdtVh6ndm95Zc35nrBbFsqLz3fGHr6DYyHgR8URbE=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			aDYD1VCbX1UQIZikBOHcRjPXPmzUntLnd7K2cjnvfoQ=
			</data>
		</dict>
		<key>Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			QlQkcAQ6Cjrzq0Uq5DVb92S5FrekQtPIFPNiJBp5RFQ=
			</data>
		</dict>
		<key>Resources/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
