<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/FlutterMacOS</key>
		<data>
		LPbIblHlvhCuSb7ZvOKL/TEJkGU=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterAppDelegate.h</key>
		<data>
		aL0Ogd6QZZ4XKwJ38BaUmoOM03E=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterAppLifecycleDelegate.h</key>
		<data>
		Q1G8pi2ylWC3NTmDZ+SuYeo50Tw=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterDartProject.h</key>
		<data>
		SpNs7IhIC7xP34Ej+LQCaEZkqik=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterEngine.h</key>
		<data>
		kox1G6/z519ooSg2qz1+lmQp5rU=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterHourFormat.h</key>
		<data>
		VjAwScWkWWSrDeetip3K4yhuwDU=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterMacOS.h</key>
		<data>
		X220zJ0/CfRpIzUgnx4z5JPkszw=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterPlatformViews.h</key>
		<data>
		uoaJTg7HTem0Jr7jxeyb551AYgM=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterPluginMacOS.h</key>
		<data>
		EiIfCAvz1W5v0RJK784BGskwZ/A=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterPluginRegistrarMacOS.h</key>
		<data>
		RvPJiKMd6ycqR1xxUM5menCXke8=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterTexture.h</key>
		<data>
		31prWLso2k5PfMMSbf5hGl+VE6Y=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterViewController.h</key>
		<data>
		UAGrzbnJpA7gxMl1Rzn73lZLHuY=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		RS15t13cXfIpKFijD9yaDymFch4=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Resources/Info.plist</key>
		<data>
		jqU87p/MJtpIAzfj0wm59ZtbI3o=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<data>
		B3Chzxf8P+/dQrjYEe6D43q2Ggo=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Resources/icudtl.dat</key>
		<data>
		ipm8hg7aB3LzsfShJfpNR0QQ4hw=
		</data>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/_CodeSignature/CodeResources</key>
		<data>
		9ZUwJadVukICutDshq7L0A2N8Go=
		</data>
		<key>macos-arm64_x86_64/dSYMs/FlutterMacOS.framework.dSYM/Contents/Info.plist</key>
		<data>
		DgvejZppCfNImyuZCRcFI8h0Usk=
		</data>
		<key>macos-arm64_x86_64/dSYMs/FlutterMacOS.framework.dSYM/Contents/Resources/DWARF/FlutterMacOS</key>
		<data>
		/xsliIP63+jhe6vljora0JIaBek=
		</data>
		<key>macos-arm64_x86_64/dSYMs/FlutterMacOS.framework.dSYM/Contents/Resources/Relocations/aarch64/FlutterMacOS.yml</key>
		<data>
		VDiweVZKvCYBTMi+P8uSNQilq6Q=
		</data>
		<key>macos-arm64_x86_64/dSYMs/FlutterMacOS.framework.dSYM/Contents/Resources/Relocations/x86_64/FlutterMacOS.yml</key>
		<data>
		1P1KWd6C+6PYwic62xQNauTskL8=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/FlutterMacOS</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FlutterMacOS</string>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/FlutterMacOS</key>
		<dict>
			<key>hash</key>
			<data>
			LPbIblHlvhCuSb7ZvOKL/TEJkGU=
			</data>
			<key>hash2</key>
			<data>
			tD6c527B3iCvKR4i0mKkOGu/p0WV5sUNTdCK19YRgxc=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			aL0Ogd6QZZ4XKwJ38BaUmoOM03E=
			</data>
			<key>hash2</key>
			<data>
			pTAmoSzOv/HLvofRvBZyxNfJ1Op07jMJo1yFlNnuj30=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterAppLifecycleDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			Q1G8pi2ylWC3NTmDZ+SuYeo50Tw=
			</data>
			<key>hash2</key>
			<data>
			oq+QhyFXFL2i2XIKH6dl24wcr0T3n22ps2+oVnGQrOc=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash</key>
			<data>
			ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
			</data>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash</key>
			<data>
			vFsZXNqjflvqKqAzsIptQaTSJho=
			</data>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash</key>
			<data>
			sUgX1PJzkvyinL5i7nS1ro/Kd5o=
			</data>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash</key>
			<data>
			SpNs7IhIC7xP34Ej+LQCaEZkqik=
			</data>
			<key>hash2</key>
			<data>
			U8q/0Ibt9q4O2HMsCdUwITtJdTx8Ljhlx+0aY83fH6s=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash</key>
			<data>
			kox1G6/z519ooSg2qz1+lmQp5rU=
			</data>
			<key>hash2</key>
			<data>
			h6MlxskInyxP7IIVoexxen/A/U19LF8qgv4WuJDDuuc=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterHourFormat.h</key>
		<dict>
			<key>hash</key>
			<data>
			VjAwScWkWWSrDeetip3K4yhuwDU=
			</data>
			<key>hash2</key>
			<data>
			Q4SLFSghL/5EFJPyLg7PNi9J/xpkVVfzro0VQiQHtrY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterMacOS.h</key>
		<dict>
			<key>hash</key>
			<data>
			X220zJ0/CfRpIzUgnx4z5JPkszw=
			</data>
			<key>hash2</key>
			<data>
			JjZR2RGsnVAbyM4Ogsw4m5m8563NSHL09XAcsF9jXVo=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash</key>
			<data>
			crQ9782ULebLQfIR+MbBkjB7d+k=
			</data>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash</key>
			<data>
			uoaJTg7HTem0Jr7jxeyb551AYgM=
			</data>
			<key>hash2</key>
			<data>
			SxKPDFth/SkJWcD6ua8FjRXytwtIhU38hBUY/kLuzAo=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterPluginMacOS.h</key>
		<dict>
			<key>hash</key>
			<data>
			EiIfCAvz1W5v0RJK784BGskwZ/A=
			</data>
			<key>hash2</key>
			<data>
			JZlDr05qFxzTIzU18aVTKM3NeffuDiIg/vWvxM17qVo=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterPluginRegistrarMacOS.h</key>
		<dict>
			<key>hash</key>
			<data>
			RvPJiKMd6ycqR1xxUM5menCXke8=
			</data>
			<key>hash2</key>
			<data>
			DngirKkW2VbOA4sWFN27fp+1t8usdFgB7Oc0JHbAVyk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash</key>
			<data>
			31prWLso2k5PfMMSbf5hGl+VE6Y=
			</data>
			<key>hash2</key>
			<data>
			JcpN4a9sv6xynlD3Ri611N5y+HoupUWp2hyrIXB/I8Y=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			UAGrzbnJpA7gxMl1Rzn73lZLHuY=
			</data>
			<key>hash2</key>
			<data>
			SMax8YOIA05ylkjLxEHZOhOa5jFK+Onca58D7h4qpjo=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			RS15t13cXfIpKFijD9yaDymFch4=
			</data>
			<key>hash2</key>
			<data>
			wgsdtVh6ndm95Zc35nrBbFsqLz3fGHr6DYyHgR8URbE=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			jqU87p/MJtpIAzfj0wm59ZtbI3o=
			</data>
			<key>hash2</key>
			<data>
			aDYD1VCbX1UQIZikBOHcRjPXPmzUntLnd7K2cjnvfoQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Resources/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			B3Chzxf8P+/dQrjYEe6D43q2Ggo=
			</data>
			<key>hash2</key>
			<data>
			QlQkcAQ6Cjrzq0Uq5DVb92S5FrekQtPIFPNiJBp5RFQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/Resources/icudtl.dat</key>
		<dict>
			<key>hash</key>
			<data>
			ipm8hg7aB3LzsfShJfpNR0QQ4hw=
			</data>
			<key>hash2</key>
			<data>
			wSU3Ai74GJkae/7UGnbY1q6WL/vA5lEax2Kl0IRef3w=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/A/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			9ZUwJadVukICutDshq7L0A2N8Go=
			</data>
			<key>hash2</key>
			<data>
			2KvwIDp+U4EXVuNt8P/YFEpZU+4OURud6W/OHpFyjwQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FlutterMacOS.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>macos-arm64_x86_64/dSYMs/FlutterMacOS.framework.dSYM/Contents/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			DgvejZppCfNImyuZCRcFI8h0Usk=
			</data>
			<key>hash2</key>
			<data>
			UXatomojf6Ul/RxEh1qNqAVoT2vTmELKlbnOKmt+ooU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/dSYMs/FlutterMacOS.framework.dSYM/Contents/Resources/DWARF/FlutterMacOS</key>
		<dict>
			<key>hash</key>
			<data>
			/xsliIP63+jhe6vljora0JIaBek=
			</data>
			<key>hash2</key>
			<data>
			aH37+8VQ69tnpX+yi/vIAIxiUfqrot3LRZgx6c//fDc=
			</data>
		</dict>
		<key>macos-arm64_x86_64/dSYMs/FlutterMacOS.framework.dSYM/Contents/Resources/Relocations/aarch64/FlutterMacOS.yml</key>
		<dict>
			<key>hash</key>
			<data>
			VDiweVZKvCYBTMi+P8uSNQilq6Q=
			</data>
			<key>hash2</key>
			<data>
			qq407xeM0GYpBGbGlPMvodgS578Z8+/Jqf4S7boQBc8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/dSYMs/FlutterMacOS.framework.dSYM/Contents/Resources/Relocations/x86_64/FlutterMacOS.yml</key>
		<dict>
			<key>hash</key>
			<data>
			1P1KWd6C+6PYwic62xQNauTskL8=
			</data>
			<key>hash2</key>
			<data>
			M5aqvnrKUW9Smz6m9jKR2Kpgz4wmqVnb0nNnq6nPgOY=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
