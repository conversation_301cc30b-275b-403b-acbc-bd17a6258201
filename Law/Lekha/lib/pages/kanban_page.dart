import 'package:flutter/material.dart';
import '../services/language_service.dart';

class KanbanPage extends StatefulWidget {
  @override
  _KanbanPageState createState() => _KanbanPageState();
}

class _KanbanPageState extends State<KanbanPage> {
  late LanguageService _languageService;
  final List<KanbanColumn> columns = [
    KanbanColumn(
      title: 'To Do',
      color: Color(0xFFF87171),
      tasks: [
        Kanban<PERSON><PERSON>('Review Contract', 'ABC Corp vs XYZ Ltd', 'High'),
        Kanban<PERSON><PERSON>('Prepare Arguments', 'CW-45234', 'Medium'),
        Kanban<PERSON><PERSON>('Client Meeting', 'Consultation', 'Low'),
      ],
    ),
    KanbanColumn(
      title: 'In Progress',
      color: Color(0xFFFBBF24),
      tasks: [
        <PERSON><PERSON><PERSON><PERSON><PERSON>('Document Review', 'WP-7890', 'High'),
        <PERSON>n<PERSON><PERSON><PERSON>('Research Case Law', 'CR-1234', 'Medium'),
      ],
    ),
    KanbanColumn(
      title: 'Review',
      color: Color(0xFF60A5FA),
      tasks: [KanbanT<PERSON>('Draft Petition', 'PIL-2024-001', 'High')],
    ),
    KanbanColumn(
      title: 'Done',
      color: Color(0xFF4ADE80),
      tasks: [
        KanbanTask('File Application', 'WP-7889', 'Medium'),
        KanbanTask('Court Hearing', 'CW-45233', 'High'),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _languageService = LanguageService();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFF0D0D0D),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            Padding(
              padding: EdgeInsets.all(24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _languageService.getText('tasks'),
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.w300,
                          fontFamily: 'Noto Sans Devanagari',
                        ),
                      ),
                      Text(
                        _languageService.getText('tasks', useEnglish: true),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.6),
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: EdgeInsets.all(14),
                    decoration: BoxDecoration(
                      color: Color(0xFF1A1A1A),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.08),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.add,
                      color: Colors.white.withOpacity(0.7),
                      size: 22,
                    ),
                  ),
                ],
              ),
            ),

            // Kanban Board
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.symmetric(horizontal: 24),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: columns
                      .map((column) => _buildKanbanColumn(column))
                      .toList(),
                ),
              ),
            ),

            SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildKanbanColumn(KanbanColumn column) {
    return Container(
      width: 280,
      margin: EdgeInsets.only(right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Column Header
          Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Color(0xFF1A1A1A),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.08),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: column.color,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 12),
                Text(
                  column.title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: column.color.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${column.tasks.length}',
                    style: TextStyle(
                      color: column.color,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 16),

          // Tasks
          ...column.tasks
              .map((task) => _buildTaskCard(task, column.color))
              .toList(),

          // Add Task Button
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(20),
            margin: EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.05),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: Colors.white.withOpacity(0.1),
                width: 1,
                style: BorderStyle.solid,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.add, color: Colors.white.withOpacity(0.6), size: 20),
                SizedBox(width: 8),
                Text(
                  'Add Task',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.6),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskCard(KanbanTask task, Color columnColor) {
    Color priorityColor;
    switch (task.priority) {
      case 'High':
        priorityColor = Color(0xFFF87171);
        break;
      case 'Medium':
        priorityColor = Color(0xFFFBBF24);
        break;
      case 'Low':
        priorityColor = Color(0xFF4ADE80);
        break;
      default:
        priorityColor = Colors.grey;
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Color(0xFF1A1A1A),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withOpacity(0.08), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  task.title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: priorityColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  task.priority,
                  style: TextStyle(
                    color: priorityColor,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            task.caseNumber,
            style: TextStyle(
              color: Colors.white.withOpacity(0.6),
              fontSize: 14,
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: columnColor.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.person, size: 14, color: columnColor),
              ),
              SizedBox(width: 8),
              Text(
                'Assigned',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.5),
                  fontSize: 12,
                ),
              ),
              Spacer(),
              Icon(
                Icons.more_horiz,
                color: Colors.white.withOpacity(0.5),
                size: 20,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class KanbanColumn {
  final String title;
  final Color color;
  final List<KanbanTask> tasks;

  KanbanColumn({required this.title, required this.color, required this.tasks});
}

class KanbanTask {
  final String title;
  final String caseNumber;
  final String priority;

  KanbanTask(this.title, this.caseNumber, this.priority);
}
